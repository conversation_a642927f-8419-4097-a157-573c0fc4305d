# 🚀 使用HBuilderX启动AI系统移动端项目

## 📥 下载安装HBuilderX

1. 访问官网：https://www.dcloud.io/hbuilderx.html
2. 下载HBuilderX标准版（免费）
3. 安装并启动HBuilderX

## 📂 打开项目

1. 启动HBuilderX
2. 点击菜单：文件 → 打开目录
3. 选择当前项目文件夹：`mobile-uniapp`
4. 项目将在左侧项目管理器中显示

## 🏃‍♂️ 运行项目

### 运行到浏览器（H5）
1. 在项目管理器中右键点击项目根目录
2. 选择：运行 → 运行到浏览器 → Chrome
3. 项目将自动编译并在浏览器中打开

### 运行到微信小程序
1. 下载安装微信开发者工具
2. 在HBuilderX中右键项目
3. 选择：运行 → 运行到小程序模拟器 → 微信开发者工具
4. 首次运行需要配置微信开发者工具路径

### 运行到手机App
1. 在HBuilderX中右键项目
2. 选择：运行 → 运行到手机或模拟器
3. 可选择真机调试或模拟器

## ✅ 项目功能验证

运行成功后，您可以测试以下功能：

### 🏠 首页
- 功能导航卡片
- 快捷操作入口
- 用户状态显示

### 🌍 翻译功能
- **文本翻译**：支持200+语言互译
- **音频翻译**：录音翻译和文件上传
- **视频翻译**：字幕提取和翻译
- **文档翻译**：PDF/Word/Excel/PPT翻译

### 🤖 数字人对话
- 多个数字人角色选择
- 智能对话交互
- 语音消息支持

### 🏪 AI智能体市场
- 智能体分类浏览
- 搜索和筛选功能
- 评分收藏系统

### 🛠️ 实用工具
- **文档转换**：多格式互转
- **图片处理**：压缩、裁剪、滤镜
- **文本处理**：格式化、统计、编码
- **二维码生成**：多类型二维码生成

### 👤 用户中心
- 用户资料管理
- 使用统计展示
- 个性化设置

## 🔧 常见问题

### Q: 项目无法运行？
A: 确保HBuilderX版本是最新的，并检查项目结构是否完整

### Q: 小程序运行失败？
A: 需要先安装对应的小程序开发工具，并在HBuilderX中配置路径

### Q: 真机调试连接失败？
A: 确保手机和电脑在同一网络，并开启USB调试

## 📱 多端测试

项目支持以下平台：
- ✅ H5 (浏览器)
- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ Android App
- ✅ iOS App

## 🎉 恭喜！

使用HBuilderX，您可以完整体验这个功能丰富的AI系统移动端应用！

所有页面都已完整实现，包含：
- 12个主要功能页面
- 完整的UI交互
- 响应式设计
- 多端适配

---

**提示**：如果您想要连接真实的后端API，请修改 `src/utils/request.ts` 中的API地址。
