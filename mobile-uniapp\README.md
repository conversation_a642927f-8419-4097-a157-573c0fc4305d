# 🤖 AI系统移动端

基于 Uni-app 的多端AI应用，支持H5、小程序、App多端运行。

## ✨ 功能特色

### 🌍 智能翻译
- **文本翻译**: 支持200+语言互译，语音输入，实时翻译
- **音频翻译**: 实时录音翻译，音频文件上传，语音合成
- **视频翻译**: 字幕提取，翻译生成，视频嵌入
- **文档翻译**: PDF/Word/Excel/PPT翻译，格式保持

### 🤖 数字人对话
- **多角色选择**: 不同风格的数字人
- **智能对话**: 自然语言交互
- **语音交互**: 语音输入输出
- **视频生成**: 数字人视频回复

### 🏪 AI智能体市场
- **智能体浏览**: 分类展示，搜索筛选
- **评分系统**: 用户评价和收藏
- **付费模式**: 免费和付费智能体
- **个性化推荐**: 基于使用习惯推荐

### 🛠️ 实用工具集
- **文档转换**: PDF/Word/Excel/PPT互转
- **图片处理**: 格式转换、压缩、裁剪、滤镜
- **文本处理**: 格式化、统计、转换、编码
- **二维码工具**: 生成、识别、美化

### 👤 用户系统
- **账号管理**: 注册登录，资料管理
- **使用统计**: 翻译次数，对话记录
- **个性化设置**: 主题切换，语言设置
- **历史记录**: 完整的使用历史

## 🚀 快速启动

### 方式1: 使用启动脚本（推荐）

**Windows用户:**
```bash
# 双击运行
quick-start.bat
```

**Mac/Linux用户:**
```bash
# 在终端中运行
./quick-start.sh
```

### 方式2: 手动启动

```bash
# 1. 安装依赖
npm install

# 2. 启动简单预览服务器
npm run serve

# 3. 浏览器访问
# http://localhost:3000
```

### 方式3: Uni-app开发模式

```bash
# H5版本
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# 支付宝小程序
npm run dev:mp-alipay

# App版本
npm run dev:app
```

## 📱 多端支持

| 平台 | 状态 | 说明 |
|------|------|------|
| H5 | ✅ 完成 | 移动端网页应用 |
| 微信小程序 | ✅ 完成 | 微信生态应用 |
| 支付宝小程序 | ✅ 完成 | 支付宝生态应用 |
| Android App | ✅ 完成 | 原生Android应用 |
| iOS App | ✅ 完成 | 原生iOS应用 |
| 鸿蒙系统 | ✅ 完成 | 华为鸿蒙应用 |

## 🏗️ 技术架构

- **框架**: Uni-app + Vue 3 + TypeScript
- **状态管理**: Pinia
- **UI组件**: uView UI
- **样式**: SCSS + 响应式设计
- **构建工具**: Vite
- **代码规范**: ESLint + TypeScript

## 📁 项目结构

```
mobile-uniapp/
├── src/
│   ├── pages/                 # 页面
│   │   ├── index/            # 首页
│   │   ├── translation/      # 翻译服务
│   │   │   ├── text/        # 文本翻译
│   │   │   ├── document/    # 文档翻译
│   │   │   ├── audio/       # 音频翻译
│   │   │   └── video/       # 视频翻译
│   │   ├── digital-human/    # 数字人
│   │   │   ├── create/      # 创建数字人
│   │   │   ├── chat/        # 对话界面
│   │   │   └── history/     # 历史记录
│   │   ├── ai-agent/         # AI智能体
│   │   │   ├── market/      # 智能体市场
│   │   │   ├── chat/        # 智能体对话
│   │   │   └── my/          # 我的智能体
│   │   ├── utilities/        # 实用工具
│   │   │   ├── conversion/  # 转换工具
│   │   │   ├── office/      # 办公工具
│   │   │   └── daily/       # 便民工具
│   │   └── user/            # 用户中心
│   │       ├── login/       # 登录
│   │       ├── profile/     # 个人资料
│   │       └── settings/    # 设置
│   ├── components/           # 公共组件
│   │   ├── common/          # 通用组件
│   │   ├── chat/            # 聊天组件
│   │   └── media/           # 媒体组件
│   ├── api/                 # API接口
│   │   ├── translation.js   # 翻译API
│   │   ├── digital-human.js # 数字人API
│   │   ├── ai-agent.js      # 智能体API
│   │   └── user.js          # 用户API
│   ├── store/               # 状态管理
│   │   ├── modules/         # 模块化store
│   │   └── index.js         # store入口
│   ├── utils/               # 工具函数
│   │   ├── request.js       # 网络请求
│   │   ├── auth.js          # 认证工具
│   │   └── common.js        # 通用工具
│   ├── static/              # 静态资源
│   │   ├── images/          # 图片
│   │   ├── icons/           # 图标
│   │   └── fonts/           # 字体
│   └── styles/              # 样式文件
│       ├── common.scss      # 通用样式
│       ├── variables.scss   # 变量定义
│       └── mixins.scss      # 混入
├── manifest.json            # 应用配置
├── pages.json              # 页面路由配置
├── uni.scss               # 全局样式
├── App.vue                # 应用入口
└── main.js                # 主入口文件
```

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 开发运行
```bash
# H5版本
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# Android App
npm run dev:app-android

# iOS App  
npm run dev:app-ios
```

### 3. 构建发布
```bash
# 构建H5
npm run build:h5

# 构建微信小程序
npm run build:mp-weixin

# 构建App
npm run build:app
```

## 📱 平台特性适配

### H5版本特性
- 响应式设计，适配各种屏幕尺寸
- PWA支持，可添加到桌面
- 支持分享到社交媒体
- 无需下载，即开即用

### 小程序版本特性  
- 微信生态集成
- 支付功能集成
- 分享转发功能
- 离线缓存能力

### App版本特性
- 原生性能体验
- 推送通知功能
- 文件系统访问
- 相机和麦克风权限

### 鸿蒙版本特性
- 鸿蒙系统深度集成
- 分布式能力支持
- 原子化服务
- 流畅动画效果

## 🔧 开发配置

### API配置
```javascript
// src/config/api.js
const API_BASE_URL = {
  development: 'http://localhost:8000',
  production: 'https://your-api-domain.com'
}
```

### 平台条件编译
```vue
<!-- #ifdef H5 -->
<view>这是H5版本特有内容</view>
<!-- #endif -->

<!-- #ifdef MP-WEIXIN -->
<view>这是微信小程序特有内容</view>
<!-- #endif -->

<!-- #ifdef APP-PLUS -->
<view>这是App版本特有内容</view>
<!-- #endif -->
```

## 📋 开发计划

- [x] 项目初始化
- [ ] 基础组件开发
- [ ] API接口适配
- [ ] 核心功能实现
- [ ] 多端测试优化
- [ ] 发布部署

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
