@echo off
echo ========================================
echo    AI System Mobile - Quick Start
echo ========================================
echo.
echo Choose your preferred way to run the project:
echo.
echo 1. View Project Overview (HTML Preview)
echo 2. Open HBuilderX Instructions
echo 3. Try npm run (may have dependency issues)
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Opening project overview...
    start index.html
) else if "%choice%"=="2" (
    echo Opening HBuilderX instructions...
    start HBuilderX启动说明.md
) else if "%choice%"=="3" (
    echo Trying npm run dev:h5...
    npm run dev:h5
) else (
    echo Invalid choice. Opening project overview...
    start index.html
)

echo.
echo For full functionality, we recommend using HBuilderX!
pause
