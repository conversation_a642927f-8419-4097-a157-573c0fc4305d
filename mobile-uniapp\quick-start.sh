#!/bin/bash

echo "========================================"
echo "   🚀 AI系统移动端快速启动脚本"
echo "========================================"
echo

echo "📦 正在安装依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败，请检查网络连接"
    exit 1
fi

echo
echo "✅ 依赖安装完成！"
echo

echo "🌐 启动开发服务器..."
echo "请在浏览器中访问: http://localhost:3000"
echo

# 尝试自动打开浏览器
if command -v open > /dev/null; then
    open http://localhost:3000
elif command -v xdg-open > /dev/null; then
    xdg-open http://localhost:3000
elif command -v start > /dev/null; then
    start http://localhost:3000
fi

npm run serve
