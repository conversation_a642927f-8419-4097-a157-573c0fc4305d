/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 导入自定义样式变量 */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
/**
 * SCSS 变量定义
 */
/* ==================== 颜色系统 ==================== */
/* ==================== 字体系统 ==================== */
/* ==================== 间距系统 ==================== */
/* ==================== 圆角系统 ==================== */
/* ==================== 阴影系统 ==================== */
/* ==================== 动画系统 ==================== */
/* ==================== 布局系统 ==================== */
/* ==================== Z-index 系统 ==================== */
/* 导入图标字体 */
/* 现代化图标字体样式 */
@font-face {
  font-family: "iconfont";
  src: url("//at.alicdn.com/t/font_xxx.woff2") format("woff2"), url("//at.alicdn.com/t/font_xxx.woff") format("woff"), url("//at.alicdn.com/t/font_xxx.ttf") format("truetype");
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 图标映射 */
.icon-home:before {
  content: "\e600";
}
.icon-home-fill:before {
  content: "\e601";
}
.icon-translate:before {
  content: "\e602";
}
.icon-translate-fill:before {
  content: "\e603";
}
.icon-robot:before {
  content: "\e604";
}
.icon-robot-fill:before {
  content: "\e605";
}
.icon-chat:before {
  content: "\e606";
}
.icon-chat-fill:before {
  content: "\e607";
}
.icon-apps:before {
  content: "\e608";
}
.icon-apps-fill:before {
  content: "\e609";
}
.icon-user:before {
  content: "\e60a";
}
.icon-user-fill:before {
  content: "\e60b";
}
.icon-document:before {
  content: "\e60c";
}
.icon-document-fill:before {
  content: "\e60d";
}
.icon-image:before {
  content: "\e60e";
}
.icon-image-fill:before {
  content: "\e60f";
}
.icon-pdf:before {
  content: "\e610";
}
.icon-video:before {
  content: "\e611";
}
.icon-audio:before {
  content: "\e612";
}
.icon-qrcode:before {
  content: "\e613";
}
.icon-text:before {
  content: "\e614";
}
.icon-star:before {
  content: "\e615";
}
.icon-star-fill:before {
  content: "\e616";
}
.icon-heart:before {
  content: "\e617";
}
.icon-heart-fill:before {
  content: "\e618";
}
.icon-bookmark:before {
  content: "\e619";
}
.icon-bookmark-fill:before {
  content: "\e61a";
}
.icon-download:before {
  content: "\e61b";
}
.icon-upload:before {
  content: "\e61c";
}
.icon-share:before {
  content: "\e61d";
}
.icon-copy:before {
  content: "\e61e";
}
.icon-delete:before {
  content: "\e61f";
}
.icon-edit:before {
  content: "\e620";
}
.icon-search:before {
  content: "\e621";
}
.icon-filter:before {
  content: "\e622";
}
.icon-sort:before {
  content: "\e623";
}
.icon-menu:before {
  content: "\e624";
}
.icon-more:before {
  content: "\e625";
}
.icon-close:before {
  content: "\e626";
}
.icon-check:before {
  content: "\e627";
}
.icon-arrow-left:before {
  content: "\e628";
}
.icon-arrow-right:before {
  content: "\e629";
}
.icon-arrow-up:before {
  content: "\e62a";
}
.icon-arrow-down:before {
  content: "\e62b";
}
.icon-plus:before {
  content: "\e62c";
}
.icon-minus:before {
  content: "\e62d";
}
.icon-refresh:before {
  content: "\e62e";
}
.icon-loading:before {
  content: "\e62f";
}
.icon-success:before {
  content: "\e630";
}
.icon-error:before {
  content: "\e631";
}
.icon-warning:before {
  content: "\e632";
}
.icon-info:before {
  content: "\e633";
}
.icon-bell:before {
  content: "\e634";
}
.icon-bell-fill:before {
  content: "\e635";
}
.icon-setting:before {
  content: "\e636";
}
.icon-setting-fill:before {
  content: "\e637";
}
.icon-crown:before {
  content: "\e638";
}
.icon-vip:before {
  content: "\e639";
}
.icon-diamond:before {
  content: "\e63a";
}
.icon-gift:before {
  content: "\e63b";
}
.icon-wallet:before {
  content: "\e63c";
}
.icon-coin:before {
  content: "\e63d";
}
.icon-calendar:before {
  content: "\e63e";
}
.icon-clock:before {
  content: "\e63f";
}
.icon-location:before {
  content: "\e640";
}
.icon-phone:before {
  content: "\e641";
}
.icon-email:before {
  content: "\e642";
}
.icon-link:before {
  content: "\e643";
}
.icon-wifi:before {
  content: "\e644";
}
.icon-bluetooth:before {
  content: "\e645";
}
.icon-camera:before {
  content: "\e646";
}
.icon-gallery:before {
  content: "\e647";
}
.icon-mic:before {
  content: "\e648";
}
.icon-volume:before {
  content: "\e649";
}
.icon-play:before {
  content: "\e64a";
}
.icon-pause:before {
  content: "\e64b";
}
.icon-stop:before {
  content: "\e64c";
}
.icon-record:before {
  content: "\e64d";
}
.icon-forward:before {
  content: "\e64e";
}
.icon-backward:before {
  content: "\e64f";
}
.icon-fullscreen:before {
  content: "\e650";
}
.icon-minimize:before {
  content: "\e651";
}
.icon-maximize:before {
  content: "\e652";
}
.icon-eye:before {
  content: "\e653";
}
.icon-eye-close:before {
  content: "\e654";
}
.icon-lock:before {
  content: "\e655";
}
.icon-unlock:before {
  content: "\e656";
}
.icon-shield:before {
  content: "\e657";
}
.icon-key:before {
  content: "\e658";
}
.icon-fingerprint:before {
  content: "\e659";
}
.icon-face:before {
  content: "\e65a";
}
.icon-scan:before {
  content: "\e65b";
}
.icon-qrcode-scan:before {
  content: "\e65c";
}
.icon-barcode:before {
  content: "\e65d";
}
.icon-print:before {
  content: "\e65e";
}
.icon-fax:before {
  content: "\e65f";
}
.icon-folder:before {
  content: "\e660";
}
.icon-folder-open:before {
  content: "\e661";
}
.icon-file:before {
  content: "\e662";
}
.icon-file-text:before {
  content: "\e663";
}
.icon-file-image:before {
  content: "\e664";
}
.icon-file-video:before {
  content: "\e665";
}
.icon-file-audio:before {
  content: "\e666";
}
.icon-file-zip:before {
  content: "\e667";
}
.icon-cloud:before {
  content: "\e668";
}
.icon-cloud-upload:before {
  content: "\e669";
}
.icon-cloud-download:before {
  content: "\e66a";
}
.icon-database:before {
  content: "\e66b";
}
.icon-server:before {
  content: "\e66c";
}
.icon-network:before {
  content: "\e66d";
}
.icon-globe:before {
  content: "\e66e";
}
.icon-language:before {
  content: "\e66f";
}
.icon-code:before {
  content: "\e670";
}
.icon-terminal:before {
  content: "\e671";
}
.icon-bug:before {
  content: "\e672";
}
.icon-tool:before {
  content: "\e673";
}
.icon-wrench:before {
  content: "\e674";
}
.icon-hammer:before {
  content: "\e675";
}
.icon-scissors:before {
  content: "\e676";
}
.icon-brush:before {
  content: "\e677";
}
.icon-palette:before {
  content: "\e678";
}
.icon-color:before {
  content: "\e679";
}
.icon-magic:before {
  content: "\e67a";
}
.icon-wand:before {
  content: "\e67b";
}
.icon-sparkles:before {
  content: "\e67c";
}
.icon-fire:before {
  content: "\e67d";
}
.icon-lightning:before {
  content: "\e67e";
}
.icon-sun:before {
  content: "\e67f";
}
.icon-moon:before {
  content: "\e680";
}
.icon-weather:before {
  content: "\e681";
}
.icon-rain:before {
  content: "\e682";
}
.icon-snow:before {
  content: "\e683";
}
.icon-wind:before {
  content: "\e684";
}
.icon-leaf:before {
  content: "\e685";
}
.icon-tree:before {
  content: "\e686";
}
.icon-flower:before {
  content: "\e687";
}
.icon-pet:before {
  content: "\e688";
}
.icon-car:before {
  content: "\e689";
}
.icon-plane:before {
  content: "\e68a";
}
.icon-train:before {
  content: "\e68b";
}
.icon-ship:before {
  content: "\e68c";
}
.icon-bike:before {
  content: "\e68d";
}
.icon-walk:before {
  content: "\e68e";
}
.icon-run:before {
  content: "\e68f";
}
.icon-sport:before {
  content: "\e690";
}
.icon-game:before {
  content: "\e691";
}
.icon-music:before {
  content: "\e692";
}
.icon-movie:before {
  content: "\e693";
}
.icon-book:before {
  content: "\e694";
}
.icon-news:before {
  content: "\e695";
}
.icon-magazine:before {
  content: "\e696";
}
.icon-education:before {
  content: "\e697";
}
.icon-graduation:before {
  content: "\e698";
}
.icon-medal:before {
  content: "\e699";
}
.icon-trophy:before {
  content: "\e69a";
}
.icon-award:before {
  content: "\e69b";
}
.icon-ribbon:before {
  content: "\e69c";
}
.icon-flag:before {
  content: "\e69d";
}
.icon-target:before {
  content: "\e69e";
}
.icon-bullseye:before {
  content: "\e69f";
}
/* 图标动画效果 */
.icon-loading {
  animation: icon-spin 1s linear infinite;
}
.icon-heart-fill {
  animation: icon-heartbeat 1.5s ease-in-out infinite;
}
.icon-bell-fill {
  animation: icon-shake 0.5s ease-in-out;
}
@keyframes icon-spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes icon-heartbeat {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}
@keyframes icon-shake {
0%, 100% {
    transform: translateX(0);
}
25% {
    transform: translateX(-2px);
}
75% {
    transform: translateX(2px);
}
}
/* 图标尺寸类 */
.icon-xs {
  font-size: 12rpx;
}
.icon-sm {
  font-size: 16rpx;
}
.icon-md {
  font-size: 20rpx;
}
.icon-lg {
  font-size: 24rpx;
}
.icon-xl {
  font-size: 32rpx;
}
.icon-2xl {
  font-size: 40rpx;
}
.icon-3xl {
  font-size: 48rpx;
}
/* 图标颜色类 */
.icon-primary {
  color: #667eea;
}
.icon-success {
  color: #10B981;
}
.icon-warning {
  color: #F59E0B;
}
.icon-danger {
  color: #EF4444;
}
.icon-info {
  color: #3B82F6;
}
.icon-secondary {
  color: #8E8E93;
}
.icon-white {
  color: #FFFFFF;
}
.icon-black {
  color: #000000;
}
/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
page {
  background-color: #F9FAFB;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
  line-height: 1.6;
  color: #111827;
  font-size: 28rpx;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 现代化按钮样式 */
.modern-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.modern-button:active {
  transform: scale(0.98);
}
.modern-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}
.modern-button.primary:hover {
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
}
.modern-button.secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1rpx solid rgba(102, 126, 234, 0.2);
}
.modern-button.secondary:hover {
  background: rgba(102, 126, 234, 0.15);
}
.modern-button.ghost {
  background: transparent;
  color: #667eea;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
}
.modern-button.ghost:hover {
  background: rgba(102, 126, 234, 0.05);
}
.modern-button.danger {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.3);
}
.modern-button.success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 20rpx rgba(16, 185, 129, 0.3);
}
.modern-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}
.modern-button.loading {
  pointer-events: none;
}
.modern-button.loading::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 32rpx;
  margin: -16rpx 0 0 -16rpx;
  border: 2rpx solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: button-loading 1s linear infinite;
}
@keyframes button-loading {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 现代化卡片样式 */
.modern-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.modern-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.modern-card.elevated {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.modern-card.bordered {
  border: 2rpx solid rgba(102, 126, 234, 0.1);
}
.modern-card.glass {
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
/* 现代化输入框样式 */
.modern-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #111827;
  background: #FFFFFF;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.modern-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.modern-input::-webkit-input-placeholder {
  color: #9CA3AF;
}
.modern-input::placeholder {
  color: #9CA3AF;
}
.modern-input.error {
  border-color: #EF4444;
}
.modern-input.error:focus {
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}
.modern-input.success {
  border-color: #10B981;
}
.modern-input.success:focus {
  box-shadow: 0 0 0 6rpx rgba(16, 185, 129, 0.1);
}
/* 现代化标签样式 */
.modern-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.modern-tag.primary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}
.modern-tag.success {
  background: rgba(16, 185, 129, 0.1);
  color: #10B981;
}
.modern-tag.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}
.modern-tag.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}
.modern-tag.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
}
.modern-tag.gray {
  background: rgba(107, 114, 128, 0.1);
  color: #6B7280;
}
/* 现代化徽章样式 */
.modern-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: linear-gradient(45deg, #EF4444, #DC2626);
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.4);
  animation: badge-pulse 2s infinite;
}
@keyframes badge-pulse {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}
/* 现代化分割线 */
.modern-divider {
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 32rpx 0;
}
.modern-divider.vertical {
  width: 1rpx;
  height: auto;
  background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 0 32rpx;
}
/* 现代化加载动画 */
.modern-loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  border-top-color: #667eea;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}
@keyframes loading-spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 现代化骨架屏 */
.modern-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}
@keyframes skeleton-loading {
0% {
    background-position: 200% 0;
}
100% {
    background-position: -200% 0;
}
}
/* 现代化渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.gradient-success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}
.gradient-warning {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}
.gradient-danger {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}
.gradient-info {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
}
.gradient-purple {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}
.gradient-pink {
  background: linear-gradient(135deg, #EC4899 0%, #DB2777 100%);
}
.gradient-cyan {
  background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
}
/* 现代化阴影 */
.shadow-modern {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.shadow-modern-lg {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.shadow-modern-xl {
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
}
.shadow-colored {
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}
/* 现代化毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}
/* 全局样式 */
page {
  background-color: #F9FAFB;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
}
/* 通用类 */
.container {
  padding: 0;
  margin: 0;
}
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.justify-center {
  justify-content: center;
}
.items-center {
  align-items: center;
}
.text-center {
  text-align: center;
}
/* 间距类 */
.p-1 {
  padding: 8rpx;
}
.p-2 {
  padding: 16rpx;
}
.p-3 {
  padding: 24rpx;
}
.p-4 {
  padding: 32rpx;
}
.m-1 {
  margin: 8rpx;
}
.m-2 {
  margin: 16rpx;
}
.m-3 {
  margin: 24rpx;
}
.m-4 {
  margin: 32rpx;
}
/* 文字颜色 */
.text-primary {
  color: #111827;
}
.text-secondary {
  color: #374151;
}
.text-tertiary {
  color: #6B7280;
}
/* 背景颜色 */
.bg-primary {
  background-color: #FFFFFF;
}
.bg-secondary {
  background-color: #F9FAFB;
}
.bg-tertiary {
  background-color: #F3F4F6;
}
/* 圆角 */
.rounded {
  border-radius: 12rpx;
}
.rounded-lg {
  border-radius: 16rpx;
}
.rounded-full {
  border-radius: 9999rpx;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}