<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI系统移动端</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 414px;
      margin: 0 auto;
      background-color: white;
      min-height: 100vh;
    }
    
    .header {
      background: linear-gradient(135deg, #2563EB, #3B82F6);
      color: white;
      padding: 60px 20px 40px;
      text-align: center;
    }
    
    .header h1 {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .header p {
      font-size: 16px;
      opacity: 0.9;
    }
    
    .features {
      padding: 20px;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .feature-item {
      background: white;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border: 1px solid #e5e7eb;
    }
    
    .feature-icon {
      width: 48px;
      height: 48px;
      background: #3B82F6;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 24px;
      color: white;
    }
    
    .feature-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .feature-desc {
      font-size: 14px;
      color: #6b7280;
    }
    
    .status {
      background: #f0f9ff;
      border: 1px solid #0ea5e9;
      border-radius: 8px;
      padding: 16px;
      margin: 20px;
    }
    
    .status-title {
      font-size: 16px;
      font-weight: 600;
      color: #0369a1;
      margin-bottom: 8px;
    }
    
    .status-list {
      list-style: none;
    }
    
    .status-list li {
      font-size: 14px;
      color: #0369a1;
      margin-bottom: 4px;
      padding-left: 20px;
      position: relative;
    }
    
    .status-list li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #10b981;
      font-weight: bold;
    }
    
    .footer {
      text-align: center;
      padding: 40px 20px;
      color: #6b7280;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🤖 AI系统移动端</h1>
      <p>基于 Uni-app 的多端AI应用</p>
    </div>
    
    <div class="features">
      <div class="feature-grid">
        <div class="feature-item">
          <div class="feature-icon">🌍</div>
          <div class="feature-title">智能翻译</div>
          <div class="feature-desc">文本、音频、视频翻译</div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🤖</div>
          <div class="feature-title">数字人对话</div>
          <div class="feature-desc">AI智能对话交互</div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🏪</div>
          <div class="feature-title">智能体市场</div>
          <div class="feature-desc">丰富的AI智能体</div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🛠️</div>
          <div class="feature-title">实用工具</div>
          <div class="feature-desc">文档转换、图片处理</div>
        </div>
      </div>
    </div>
    
    <div class="status">
      <div class="status-title">📋 项目状态</div>
      <ul class="status-list">
        <li>基础架构已完成</li>
        <li>核心页面已创建</li>
        <li>翻译功能已实现</li>
        <li>实用工具已开发</li>
        <li>用户系统已完善</li>
        <li>多端适配已完成</li>
      </ul>
    </div>
    
    <div class="footer">
      <p>🚀 项目完成度: 95%</p>
      <p>支持 H5、小程序、App 多端运行</p>
    </div>
  </div>
</body>
</html>
